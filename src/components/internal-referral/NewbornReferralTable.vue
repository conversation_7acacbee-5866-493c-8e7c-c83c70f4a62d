<template>
  <div
    class="newborn-referral-table-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          新生儿转诊申请列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
    <el-table-column prop="rid" label="申请单号" min-width="120" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="font-mono text-sm">{{ row.rid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="maternity_name" label="产妇姓名" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.maternity_name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.maternity_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="baby_name" label="婴儿姓名" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="32" class="mr-2 bg-blue-100 text-blue-600">
              {{ row.baby_name ? row.baby_name.charAt(0) : '婴' }}
            </el-avatar>
            <span class="font-medium">{{ row.baby_name || '未命名' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="room_number" label="房间号" min-width="100">
        <template #default="{ row }">
          <el-tag type="info" size="small" v-if="row.room_number && row.room_number !== '-'">
            {{ row.room_number }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="referral_department" label="转诊科室" min-width="120">
        <template #default="{ row }">
          <span class="font-medium">{{ row.referral_department }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="main_symptoms_and_reasons" label="主要症状及原因" min-width="200">
        <template #default="{ row }">
          <el-tooltip
            :content="row.main_symptoms_and_reasons"
            placement="top"
            :disabled="row.main_symptoms_and_reasons.length <= 10"
          >
            <span class="text-gray-700">
              {{
                row.main_symptoms_and_reasons.length > 10
                  ? row.main_symptoms_and_reasons.substring(0, 10) + '...'
                  : row.main_symptoms_and_reasons
              }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="emergency_level" label="紧急程度" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getEmergencyLevelTagType(row.emergency_level)" size="small">
            {{ row.emergency_level_label }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="application_time" label="申请时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.application_time) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.application_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="actual_return_time" label="返回时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.actual_return_time) }}</div>
            <div class="text-xs text-gray-400">{{ row.actual_return_time ? formatTime(row.actual_return_time) : '-' }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="approval_status" label="审批状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getAuditStatusTagType(row.approval_status)" size="small">
            {{ row.approval_status_label }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="referral_status" label="转诊状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getMedicalReferralStatusTagType(row.referral_status)" size="small">
            {{ row.referral_status_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="320" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              v-if="row.approval_status === 'PENDING_AUDIT' || row.approval_status === 'PENDING'"
              @click.stop="handleUpdateStatus(row)"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              处理申请
            </el-button>

            <!-- 状态操作按钮 -->
            <el-button
              v-if="row.referral_status === 'PENDING_REFERRAL'"
              @click.stop="handleStatusChange(row, 'REFERRALING')"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              标记转诊中
            </el-button>

            <el-button
              v-if="row.referral_status === 'REFERRALING'"
              @click.stop="handleStatusChange(row, 'REFERRALLED')"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              标记已转诊
            </el-button>

            <el-button
              v-if="row.referral_status === 'REFERRALLED'"
              @click.stop="handleReturnConfirm(row)"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              标记已返回
            </el-button>

            <el-button
              v-if="row.approval_status === 'PENDING_AUDIT' || row.approval_status === 'PENDING'"
              @click.stop="handleEdit(row)"
              type="default"
              size="small"
            >
              编辑
            </el-button>
            <el-button @click.stop="handleView(row)" type="default" size="small"> 查看 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <ReferralDetailDialog
      v-model="detailDialogVisible"
      :item-id="selectedItemId"
      referral-type="newborn"
      @close="handleDetailDialogClose"
      @process="handleProcessFromDetail"
    />

    <!-- 返回确认对话框 -->
    <ReturnConfirmDialog
      v-model="returnConfirmDialogVisible"
      :referral-id="selectedReturnReferralId"
      referral-type="newborn"
      @confirm="handleReturnConfirmSubmit"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { List } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get, put } from '@/utils/request.js'
import {
  getAuditStatusTagType,
  getEmergencyLevelTagType,
  getMedicalReferralStatusTagType,
} from '@/utils/constants.js'
import ReferralDetailDialog from './ReferralDetailDialog.vue'
import ReturnConfirmDialog from './ReturnConfirmDialog.vue'

const emit = defineEmits(['edit', 'view', 'update-status', 'row-click'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/newborn-medical-referral/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 详情对话框相关
const detailDialogVisible = ref(false)
const selectedItemId = ref(null)

// 返回确认对话框相关
const returnConfirmDialogVisible = ref(false)
const selectedReturnReferralId = ref(null)
const selectedReturnRow = ref(null)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的apt字段，如果存在且为日期格式，转换为日期时间格式
    const processedFilters = { ...props.filters }
    if (processedFilters.apt && /^\d{4}-\d{2}-\d{2}$/.test(processedFilters.apt)) {
      processedFilters.apt = `${processedFilters.apt} 00:00:00`
    }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0
  } catch (error) {
    console.error('获取新生儿转诊申请列表失败:', error)
    ElMessage.error('获取新生儿转诊申请列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleView = (row) => {
  selectedItemId.value = row.rid
  detailDialogVisible.value = true
  emit('view', row)
}

const handleUpdateStatus = (row) => {
  emit('update-status', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 处理状态变更
const handleStatusChange = async (row, targetStatus) => {
  const statusConfig = {
    REFERRALING: {
      confirmText: '确定要标记该转诊单为"转诊中"状态吗？',
      url: `customer-service/newborn-medical-referral/mark-referral-ing/${row.rid}/`,
      successText: '已成功标记为转诊中',
    },
    REFERRALLED: {
      confirmText: '确定要标记该转诊单为"已转诊"状态吗？',
      url: `customer-service/newborn-medical-referral/mark-referral-completed/${row.rid}/`,
      successText: '已成功标记为已转诊',
    },
  }

  const config = statusConfig[targetStatus]
  if (!config) {
    ElMessage.error('无效的状态操作')
    return
  }

  try {
    await ElMessageBox.confirm(config.confirmText, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--primary',
    })

    // 调用对应的API
    await put(config.url)

    ElMessage.success(config.successText)

    // 刷新表格数据
    refresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态变更失败:', error)
      ElMessage.error('操作失败，请稍后重试')
    }
  }
}

// 处理返回确认
const handleReturnConfirm = (row) => {
  selectedReturnReferralId.value = row.rid
  selectedReturnRow.value = row
  returnConfirmDialogVisible.value = true
}

// 处理返回确认提交
const handleReturnConfirmSubmit = async (data) => {
  try {
    const url = `customer-service/newborn-medical-referral/mark-referral-returned/${selectedReturnReferralId.value}/`

    // 调用API，将文件URL列表和备注作为参数
    await put(url, data)

    ElMessage.success('已成功标记为已返回')

    // 关闭对话框
    returnConfirmDialogVisible.value = false

    // 刷新表格数据
    refresh()
  } catch (error) {
    console.error('标记已返回失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 详情对话框事件处理
const handleDetailDialogClose = () => {
  detailDialogVisible.value = false
  selectedItemId.value = null
}

const handleProcessFromDetail = (data) => {
  emit('update-status', data)
  handleDetailDialogClose()
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.newborn-referral-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.newborn-referral-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
